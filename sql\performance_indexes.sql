-- Psychiatry EMR - Performance Indexes
-- Performance-optimized indexes for the Psychiatry EMR

-- Patient table indexes
CREATE INDEX IF NOT EXISTS idx_patient_dob ON patient(dob);
CREATE INDEX IF NOT EXISTS idx_patient_created_at ON patient(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_patient_active ON patient(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_patient_merged_into ON patient(merged_into) WHERE merged_into IS NOT NULL;

-- Composite index for name + DOB uniqueness checking (on encrypted data)
CREATE INDEX IF NOT EXISTS idx_patient_name_dob ON patient(name_encrypted, dob) WHERE is_active = true;

-- Audit log indexes for performance monitoring
CREATE INDEX IF NOT EXISTS idx_audit_user_ts ON audit_log(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_table_record ON audit_log(table_name, record_id);
CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_failed ON audit_log(success, timestamp DESC) WHERE success = false;

-- User access indexes
CREATE INDEX IF NOT EXISTS idx_user_access_patient ON user_patient_access(patient_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_access_user ON user_patient_access(user_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_access_granted ON user_patient_access(granted_at DESC);

-- Clinical data indexes
CREATE INDEX IF NOT EXISTS idx_present_illness_patient ON present_illness(patient_id);
CREATE INDEX IF NOT EXISTS idx_present_illness_date ON present_illness(assessment_date DESC);
CREATE INDEX IF NOT EXISTS idx_present_illness_diagnosis ON present_illness(primary_diagnosis);

-- Partial indexes for active records only (more efficient)
CREATE INDEX IF NOT EXISTS idx_patient_active_only ON patient(id, created_at) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_access_active_only ON user_patient_access(user_id, patient_id) WHERE is_active = true;

-- Text search indexes (requires pg_trgm extension)
-- These would enable better encrypted text search if implemented
-- CREATE INDEX IF NOT EXISTS idx_patient_name_trgm ON patient USING gin(name_encrypted gin_trgm_ops);

COMMENT ON INDEX idx_patient_dob IS 'Index for age-based queries and statistics';
COMMENT ON INDEX idx_audit_user_ts IS 'Primary audit query index for user activity reports';
COMMENT ON INDEX idx_patient_active_only IS 'Partial index covering most patient queries';
