"""
Psychiatry EMR - Audit Models
Database models for comprehensive audit logging.
"""

import reflex as rx
from sqlmodel import Field
from datetime import datetime
from typing import Optional

class AuditLog(rx.Model, table=True):
    """Comprehensive audit logging with better structure"""
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id")
    timestamp: datetime = Field(default_factory=datetime.now)
    action: str  # CREATE, READ, UPDATE, DELETE, SEARCH, ACCESS_DENIED, etc.
    table_name: str
    record_id: Optional[int] = Field(default=None)
    old_values: Optional[str] = Field(default=None)  # JSON string
    new_values: Optional[str] = Field(default=None)  # JSON string
    ip_address: Optional[str] = Field(default=None)
    user_agent: Optional[str] = Field(default=None)
    session_id: Optional[str] = Field(default=None)
    success: bool = Field(default=True)
    error_message: Optional[str] = Field(default=None)
