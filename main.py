"""
Psychiatry EMR - Main Application Entry Point
Production-ready application startup with comprehensive initialization.
"""

import reflex as rx
import logging
import os
import getpass
from pathlib import Path
import sys

from security.encryption import initialize_encryption, generate_salt
from services.database import get_engine, DB_INDEXES
from config.settings import get_settings, generate_env_template

def setup_logging():
    """Configure comprehensive logging"""
    try:
        settings = get_settings()
        logging.basicConfig(
            level=getattr(logging, settings.log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            handlers=[
                logging.FileHandler(settings.log_file),
                logging.StreamHandler()
            ]
        )

        # Set specific loggers
        logging.getLogger('sqlalchemy.engine').setLevel(
            logging.INFO if settings.debug_mode else logging.WARNING
        )
    except Exception as e:
        # Fallback logging if settings fail
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler()]
        )
        print(f"Warning: Could not load settings for logging: {e}")

def initialize_database():
    """Initialize database schema and indexes"""
    logger = logging.getLogger(__name__)

    try:
        # Create all tables
        engine = get_engine()

        # Import all models to ensure they're registered
        from models.patient import Patient, UserPatientAccess
        from models.clinical import PresentIllness
        from models.audit import AuditLog

        rx.Model.metadata.create_all(engine)
        logger.info("Database tables created/verified")

        # Apply performance indexes
        from sqlmodel import text
        with engine.connect() as conn:
            conn.execute(text(DB_INDEXES))
            conn.commit()
        logger.info("Performance indexes applied")

    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise

def get_master_password() -> str:
    """Get master password for encryption with validation"""
    if os.getenv("MASTER_PASSWORD"):
        print("⚠️  WARNING: Using master password from environment variable")
        print("   This is not recommended for production use.")
        return os.getenv("MASTER_PASSWORD")

    try:
        settings = get_settings()
        print(f"\n🏥 {settings.app_name} v{settings.app_version}")
    except:
        print(f"\n🏥 Psychiatry EMR v1.0.0")

    print("=" * 50)

    while True:
        password = getpass.getpass("🔐 Enter master password for encryption: ")
        if len(password) < 8:
            print("❌ Password must be at least 8 characters long")
            continue

        confirm = getpass.getpass("🔐 Confirm master password: ")
        if password != confirm:
            print("❌ Passwords do not match")
            continue

        return password

def check_environment():
    """Check that all required environment variables are set"""
    logger = logging.getLogger(__name__)

    # Check if .env exists
    if not Path('.env').exists():
        print("❌ .env file not found!")
        print("📝 Creating template .env file...")

        with open('.env', 'w') as f:
            f.write(generate_env_template())

        print("✅ Template .env file created")
        print("🔧 Please review and update the values, then restart the application")
        sys.exit(1)

    try:
        # This will validate all required settings
        settings = get_settings()
        _ = settings.encryption_salt
        _ = settings.secret_key
        logger.info("Environment configuration validated")
    except Exception as e:
        logger.error(f"Environment validation failed: {e}")
        print(f"❌ Environment validation failed: {e}")
        print("🔧 Please check your .env file")
        sys.exit(1)

def initialize_application():
    """Initialize application with comprehensive setup"""
    logger = logging.getLogger(__name__)

    try:
        settings = get_settings()
        logger.info(f"Initializing {settings.app_name} v{settings.app_version}")
    except:
        logger.info("Initializing Psychiatry EMR v1.0.0")

    try:
        # Check environment first
        check_environment()

        # Get master password for encryption
        master_password = get_master_password()

        # Initialize encryption service
        crypto_service = initialize_encryption(master_password)
        logger.info("✅ Encryption service initialized")

        # Initialize database
        initialize_database()
        logger.info("✅ Database initialized")

        # Store crypto service globally for dependency injection
        # In a real implementation, you'd use a proper DI container
        rx.State._crypto_service = crypto_service

        print("🚀 Application initialization complete!")
        print(f"📊 Starting server on http://localhost:3000")

    except KeyboardInterrupt:
        print("\n❌ Initialization cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Application initialization failed: {e}")
        print(f"❌ Initialization failed: {e}")
        sys.exit(1)

# Setup logging first
setup_logging()
logger = logging.getLogger(__name__)

# Initialize application
initialize_application()

# Create Reflex app with enhanced configuration
try:
    settings = get_settings()

    app = rx.App(
        state=rx.State,
        style={
            "font_family": "Inter, system-ui, sans-serif",
            "background_color": "#f8fafc",
            "color": "#1e293b",
        },
        stylesheets=[
            "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
        ],
        theme=rx.theme(
            appearance="light",
            has_background=True,
            radius="medium",
            accent_color="blue",
        ),
    )

    # Configure session settings
    app.state_manager.timeout = settings.session_timeout_minutes * 60

except Exception as e:
    logger.warning(f"Could not load settings for app configuration: {e}")
    # Fallback app configuration
    app = rx.App(
        state=rx.State,
        style={
            "font_family": "Inter, system-ui, sans-serif",
            "background_color": "#f8fafc",
            "color": "#1e293b",
        },
    )

# Enhanced index page
@rx.page("/")
def index():
    return rx.container(
        rx.vstack(
            rx.heading("🏥 Psychiatry EMR", size="9"),
            rx.text("Secure Patient Management System", size="5", color="gray"),
            rx.divider(),
            rx.text("✅ Application successfully initialized!", size="4", color="green"),
            rx.text("🔐 Encryption service active", size="3"),
            rx.text("🗄️ Database connected", size="3"),
            rx.text("📊 Ready for patient management", size="3"),
            rx.divider(),
            rx.text("Full UI implementation will be added in Phase 6", size="2", color="gray"),
            spacing="4",
            align="center",
            min_height="100vh",
            justify="center",
        ),
        max_width="800px",
        margin="0 auto",
        padding="2rem",
    )

# Add the page to the app
app.add_page(index)

if __name__ == "__main__":
    try:
        settings = get_settings()
        logger.info("Starting Psychiatry EMR application...")
        app.run(
            host="127.0.0.1" if settings.debug_mode else "0.0.0.0",
            port=3000,
            debug=settings.debug_mode
        )
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        print(f"❌ Failed to start application: {e}")
        sys.exit(1)
