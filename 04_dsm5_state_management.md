# DSM-5-TR Engine & State Management

## Enhanced DSM-5-TR Rule Engine with Validation

```python
# services/dsm5_engine.py
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pydantic import BaseModel, validator
import yaml
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class CriterionRule(BaseModel):
    """Individual DSM-5-TR criterion with Pydantic validation"""
    id: str
    description: str
    required: bool = True
    # Remove unused weight field - DSM-5-TR uses count-based criteria
    
    @validator('id')
    def id_must_be_valid(cls, v):
        if not v or not v.strip():
            raise ValueError('Criterion ID cannot be empty')
        return v.strip()

class DisorderCriteria(BaseModel):
    """Complete disorder criteria set with validation"""
    disorder_code: str
    name: str
    criteria: List[CriterionRule]
    minimum_criteria: int
    duration_required: bool = True
    exclusion_rules: List[str] = []
    
    @validator('minimum_criteria')
    def minimum_must_be_reasonable(cls, v, values):
        if 'criteria' in values and v > len(values['criteria']):
            raise ValueError('Minimum criteria cannot exceed total criteria count')
        if v < 1:
            raise ValueError('Minimum criteria must be at least 1')
        return v

class DSM5Configuration(BaseModel):
    """Complete DSM-5-TR configuration with validation"""
    version: str
    disorders: List[DisorderCriteria]

class DSM5RuleEngine:
    """Rule engine for DSM-5-TR diagnostic criteria with robust validation"""
    
    def __init__(self, criteria_file: str = "config/dsm5_criteria.yaml"):
        self.criteria_file = Path(criteria_file)
        self.disorders: Dict[str, DisorderCriteria] = {}
        self._load_and_validate_criteria()
    
    def _load_and_validate_criteria(self) -> None:
        """Load and validate DSM-5-TR criteria from YAML file"""
        try:
            if not self.criteria_file.exists():
                logger.info("DSM-5-TR criteria file not found, creating default")
                self._create_default_criteria_file()
            
            with open(self.criteria_file, 'r') as f:
                raw_data = yaml.safe_load(f)
            
            # Validate with Pydantic
            config = DSM5Configuration(**raw_data)
            
            # Build disorders dictionary
            for disorder in config.disorders:
                self.disorders[disorder.disorder_code] = disorder
            
            logger.info(f"Loaded {len(self.disorders)} DSM-5-TR disorders")
            
        except yaml.YAMLError as e:
            logger.error(f"YAML parsing error in DSM-5-TR criteria: {e}")
            raise ValueError(f"Invalid YAML in DSM-5-TR criteria file: {e}")
        except Exception as e:
            logger.error(f"Failed to load DSM-5-TR criteria: {e}")
            raise
    
    def evaluate_criteria(self, disorder_code: str, responses: Dict[str, bool]) -> Dict[str, Any]:
        """Evaluate diagnostic criteria and return results (count-based, not weight-based)"""
        if disorder_code not in self.disorders:
            raise ValueError(f"Unknown disorder code: {disorder_code}")
        
        disorder = self.disorders[disorder_code]
        met_criteria = []
        
        for criterion in disorder.criteria:
            if responses.get(criterion.id, False):
                met_criteria.append(criterion.id)
        
        criteria_met = len(met_criteria) >= disorder.minimum_criteria
        
        return {
            'disorder_code': disorder_code,
            'disorder_name': disorder.name,
            'criteria_met': criteria_met,
            'met_criteria_count': len(met_criteria),
            'required_criteria': disorder.minimum_criteria,
            'met_criteria_ids': met_criteria,
            'duration_required': disorder.duration_required,
            'confidence_level': len(met_criteria) / len(disorder.criteria) if disorder.criteria else 0
        }
    
    def get_disorder_criteria(self, disorder_code: str) -> Optional[DisorderCriteria]:
        """Get criteria for a specific disorder"""
        return self.disorders.get(disorder_code)
    
    def list_available_disorders(self) -> List[Dict[str, str]]:
        """List all available disorders"""
        return [
            {'code': code, 'name': disorder.name}
            for code, disorder in self.disorders.items()
        ]
    
    def _create_default_criteria_file(self):
        """Create default DSM-5-TR criteria file with comprehensive validation"""
        default_criteria = {
            'version': '5-TR',
            'disorders': [
                {
                    'disorder_code': '296.22',
                    'name': 'Major Depressive Disorder, Single Episode, Moderate',
                    'minimum_criteria': 5,
                    'duration_required': True,
                    'criteria': [
                        {'id': 'mdd_1', 'description': 'Depressed mood most of the day, nearly every day', 'required': True},
                        {'id': 'mdd_2', 'description': 'Markedly diminished interest or pleasure in activities', 'required': True},
                        {'id': 'mdd_3', 'description': 'Significant weight loss/gain or appetite changes'},
                        {'id': 'mdd_4', 'description': 'Insomnia or hypersomnia nearly every day'},
                        {'id': 'mdd_5', 'description': 'Psychomotor agitation or retardation'},
                        {'id': 'mdd_6', 'description': 'Fatigue or loss of energy nearly every day'},
                        {'id': 'mdd_7', 'description': 'Feelings of worthlessness or inappropriate guilt'},
                        {'id': 'mdd_8', 'description': 'Diminished ability to think or concentrate'},
                        {'id': 'mdd_9', 'description': 'Recurrent thoughts of death or suicidal ideation'}
                    ]
                },
                {
                    'disorder_code': '300.02',
                    'name': 'Generalized Anxiety Disorder',
                    'minimum_criteria': 3,
                    'duration_required': True,
                    'criteria': [
                        {'id': 'gad_1', 'description': 'Excessive anxiety and worry, occurring more days than not for at least 6 months', 'required': True},
                        {'id': 'gad_2', 'description': 'Difficulty controlling the worry', 'required': True},
                        {'id': 'gad_3', 'description': 'Restlessness or feeling keyed up or on edge'},
                        {'id': 'gad_4', 'description': 'Being easily fatigued'},
                        {'id': 'gad_5', 'description': 'Difficulty concentrating or mind going blank'},
                        {'id': 'gad_6', 'description': 'Irritability'},
                        {'id': 'gad_7', 'description': 'Muscle tension'},
                        {'id': 'gad_8', 'description': 'Sleep disturbance'}
                    ]
                }
            ]
        }
        
        with open(self.criteria_file, 'w') as f:
            yaml.dump(default_criteria, f, default_flow_style=False, indent=2)
```

## Enhanced State Management with Pydantic

```python
# states/patient_state.py
import reflex as rx
from typing import Optional, List, Dict, Any
import logging
from datetime import datetime

from models.patient import PatientData
from services.patient_service import PatientService
from services.database import get_db_session
from security.encryption import initialize_encryption
from config.settings import settings

logger = logging.getLogger(__name__)

class PatientState(rx.State):
    """Patient management state with Pydantic data contracts and toast notifications"""
    
    # Use Pydantic model instead of dict
    current_patient: Optional[PatientData] = None
    search_term: str = ""
    search_results: List[PatientData] = []
    potential_duplicates: List[dict] = []
    
    # Pagination
    current_page: int = 1
    page_size: int = 25
    total_results: int = 0
    total_pages: int = 0
    
    # UI state
    is_loading: bool = False
    error_message: str = ""
    success_message: str = ""  # For toast notifications
    
    @rx.var
    def current_user_id(self) -> int:
        """Get current user ID from JWT token"""
        if self.router.session.client_token:
            # Extract from JWT - implementation depends on Reflex auth structure
            return 1  # Placeholder
        return 0
    
    def show_success(self, message: str):
        """Show success toast notification"""
        self.success_message = message
        # Auto-clear after 3 seconds (would need timer in real implementation)
    
    def show_error(self, message: str):
        """Show error toast notification"""
        self.error_message = message
    
    def clear_messages(self):
        """Clear all notification messages"""
        self.error_message = ""
        self.success_message = ""
    
    def search_patients(self, term: str = None, page: int = None):
        """Search patients with pagination and proper error handling"""
        if term is not None:
            self.search_term = term
            self.current_page = 1  # Reset to first page on new search
        
        if page is not None:
            self.current_page = page
        
        if not self.search_term.strip():
            self.search_results = []
            self.total_results = 0
            self.total_pages = 0
            return
        
        self.is_loading = True
        self.clear_messages()
        
        try:
            # Get encryption service (would be injected in real implementation)
            master_password = "temp_password"  # Would come from startup
            crypto_service = initialize_encryption(master_password)
            
            with get_db_session() as db:
                service = PatientService(db, self.current_user_id, crypto_service)
                result = service.search_patients(
                    self.search_term, 
                    page=self.current_page, 
                    page_size=self.page_size
                )
                
                self.search_results = result["patients"]
                self.total_results = result["total"]
                self.total_pages = result["total_pages"]
            
            logger.info(f"Search completed: {len(self.search_results)} results")
        except Exception as e:
            logger.error(f"Search failed: {e}")
            self.show_error("Search failed. Please try again.")
            self.search_results = []
            self.total_results = 0
            self.total_pages = 0
        finally:
            self.is_loading = False
    
    def load_patient(self, patient_id: int):
        """Load patient with comprehensive error handling"""
        self.is_loading = True
        self.clear_messages()
        
        try:
            master_password = "temp_password"  # Would come from startup
            crypto_service = initialize_encryption(master_password)
            
            with get_db_session() as db:
                service = PatientService(db, self.current_user_id, crypto_service)
                patient = service.get_patient(patient_id)
                
                if patient:
                    self.current_patient = patient
                    logger.info(f"Patient loaded: {patient_id}")
                    self.show_success(f"Loaded patient: {patient.name}")
                else:
                    self.show_error("Patient not found or access denied")
                    logger.warning(f"Patient load failed: {patient_id}")
        except Exception as e:
            logger.error(f"Failed to load patient {patient_id}: {e}")
            self.show_error("Failed to load patient data")
        finally:
            self.is_loading = False
    
    def create_patient(self, patient_data: dict):
        """Create new patient with validation"""
        self.is_loading = True
        self.clear_messages()
        
        try:
            # Validate with Pydantic
            validated_data = PatientData(**patient_data)
            
            master_password = "temp_password"  # Would come from startup
            crypto_service = initialize_encryption(master_password)
            
            with get_db_session() as db:
                service = PatientService(db, self.current_user_id, crypto_service)
                new_patient = service.create_patient(validated_data)
                self.current_patient = new_patient
                
            logger.info(f"Patient created successfully")
            self.show_success(f"Patient created: {new_patient.name}")
        except ValueError as e:
            logger.warning(f"Validation error: {e}")
            self.show_error(f"Invalid data: {e}")
        except Exception as e:
            logger.error(f"Failed to create patient: {e}")
            self.show_error("Failed to create patient")
        finally:
            self.is_loading = False
    
    def check_duplicates(self, patient_data: dict):
        """Check for potential duplicate patients"""
        try:
            validated_data = PatientData(**patient_data)
            
            master_password = "temp_password"  # Would come from startup
            crypto_service = initialize_encryption(master_password)
            
            with get_db_session() as db:
                service = PatientService(db, self.current_user_id, crypto_service)
                self.potential_duplicates = service.find_potential_duplicates(validated_data)
                
        except Exception as e:
            logger.error(f"Duplicate check failed: {e}")
            self.potential_duplicates = []
    
    def next_page(self):
        """Go to next page of search results"""
        if self.current_page < self.total_pages:
            self.search_patients(page=self.current_page + 1)
    
    def prev_page(self):
        """Go to previous page of search results"""
        if self.current_page > 1:
            self.search_patients(page=self.current_page - 1)
    
    def go_to_page(self, page: int):
        """Go to specific page"""
        if 1 <= page <= self.total_pages:
            self.search_patients(page=page)
```

## Clinical Assessment State Management

```python
# states/clinical_state.py
import reflex as rx
from typing import Optional, List, Dict, Any
import logging
from datetime import date

from models.clinical import PresentIllnessData
from services.clinical_service import ClinicalService
from services.dsm5_engine import DSM5RuleEngine, DisorderCriteria
from services.database import get_db_session
from security.encryption import initialize_encryption

logger = logging.getLogger(__name__)

class ClinicalState(rx.State):
    """Clinical assessment state with DSM-5-TR integration"""
    
    # Current assessment
    current_assessment: Optional[PresentIllnessData] = None
    patient_assessments: List[PresentIllnessData] = []
    
    # DSM-5-TR state
    available_disorders: List[Dict[str, str]] = []
    selected_disorder: str = ""
    disorder_criteria: Optional[DisorderCriteria] = None
    criterion_responses: Dict[str, bool] = {}
    dsm5_evaluation: Optional[Dict[str, Any]] = None
    
    # Form state
    assessment_form: Dict[str, Any] = {
        "patient_id": 0,
        "assessment_date": "",
        "chief_complaint": "",
        "history_present_illness": "",
        "primary_diagnosis": "",
        "secondary_diagnoses": "",
        "treatment_plan": ""
    }
    
    # UI state
    is_loading: bool = False
    error_message: str = ""
    success_message: str = ""
    
    @rx.var
    def current_user_id(self) -> int:
        """Get current user ID from authentication"""
        return 1  # Placeholder
    
    def load_disorders(self):
        """Load available DSM-5-TR disorders"""
        try:
            engine = DSM5RuleEngine()
            self.available_disorders = engine.list_available_disorders()
            logger.info(f"Loaded {len(self.available_disorders)} disorders")
        except Exception as e:
            logger.error(f"Failed to load disorders: {e}")
            self.show_error("Failed to load DSM-5-TR criteria")
    
    def select_disorder(self, disorder_code: str):
        """Select disorder and load its criteria"""
        self.selected_disorder = disorder_code
        self.criterion_responses = {}
        self.dsm5_evaluation = None
        
        try:
            engine = DSM5RuleEngine()
            self.disorder_criteria = engine.get_disorder_criteria(disorder_code)
            
            if self.disorder_criteria:
                # Initialize responses
                for criterion in self.disorder_criteria.criteria:
                    self.criterion_responses[criterion.id] = False
                
                # Update form
                self.assessment_form["primary_diagnosis"] = disorder_code
                
                logger.info(f"Selected disorder: {disorder_code}")
            else:
                self.show_error("Disorder criteria not found")
                
        except Exception as e:
            logger.error(f"Failed to load disorder criteria: {e}")
            self.show_error("Failed to load disorder criteria")
    
    def update_criterion_response(self, criterion_id: str, value: bool):
        """Update DSM-5-TR criterion response and re-evaluate"""
        self.criterion_responses[criterion_id] = value
        
        # Auto-evaluate criteria
        if self.selected_disorder:
            self.evaluate_dsm5_criteria()
    
    def evaluate_dsm5_criteria(self):
        """Evaluate current DSM-5-TR criterion responses"""
        if not self.selected_disorder or not self.criterion_responses:
            return
        
        try:
            engine = DSM5RuleEngine()
            self.dsm5_evaluation = engine.evaluate_criteria(
                self.selected_disorder, 
                self.criterion_responses
            )
            
            logger.debug(f"DSM-5 evaluation: {self.dsm5_evaluation}")
            
        except Exception as e:
            logger.error(f"DSM-5 evaluation failed: {e}")
            self.show_error("Failed to evaluate criteria")
    
    def create_assessment(self):
        """Create new clinical assessment with DSM-5-TR results"""
        self.is_loading = True
        self.clear_messages()
        
        try:
            # Validate required fields
            if not self.assessment_form["patient_id"]:
                raise ValueError("Patient ID is required")
            if not self.assessment_form["chief_complaint"].strip():
                raise ValueError("Chief complaint is required")
            if not self.assessment_form["history_present_illness"].strip():
                raise ValueError("History of present illness is required")
            
            # Create assessment data
            assessment_data = PresentIllnessData(
                patient_id=self.assessment_form["patient_id"],
                assessment_date=date.fromisoformat(self.assessment_form["assessment_date"]),
                chief_complaint=self.assessment_form["chief_complaint"],
                history_present_illness=self.assessment_form["history_present_illness"],
                primary_diagnosis=self.assessment_form["primary_diagnosis"],
                secondary_diagnoses=self.assessment_form["secondary_diagnoses"],
                treatment_plan=self.assessment_form["treatment_plan"]
            )
            
            # Get encryption service
            master_password = "temp_password"  # Would come from startup
            crypto_service = initialize_encryption(master_password)
            
            with get_db_session() as db:
                service = ClinicalService(db, self.current_user_id, crypto_service)
                
                # Create assessment with DSM-5 responses
                new_assessment = service.create_assessment(
                    assessment_data, 
                    self.criterion_responses if self.criterion_responses else None
                )
                
                self.current_assessment = new_assessment
                
                # Reload patient assessments
                self.load_patient_assessments(assessment_data.patient_id)
            
            self.show_success("Assessment created successfully")
            logger.info(f"Assessment created for patient {assessment_data.patient_id}")
            
        except ValueError as e:
            self.show_error(f"Validation error: {e}")
        except Exception as e:
            logger.error(f"Failed to create assessment: {e}")
            self.show_error("Failed to create assessment")
        finally:
            self.is_loading = False
    
    def load_patient_assessments(self, patient_id: int):
        """Load all assessments for a patient"""
        try:
            master_password = "temp_password"  # Would come from startup
            crypto_service = initialize_encryption(master_password)
            
            with get_db_session() as db:
                service = ClinicalService(db, self.current_user_id, crypto_service)
                self.patient_assessments = service.get_patient_assessments(patient_id)
            
            logger.info(f"Loaded {len(self.patient_assessments)} assessments for patient {patient_id}")
            
        except Exception as e:
            logger.error(f"Failed to load patient assessments: {e}")
            self.show_error("Failed to load patient assessments")
    
    def update_assessment_form(self, field: str, value: Any):
        """Update assessment form field"""
        self.assessment_form[field] = value
        
        # If primary diagnosis changed, update disorder selection
        if field == "primary_diagnosis" and value != self.selected_disorder:
            self.select_disorder(value)
    
    def reset_assessment_form(self):
        """Reset assessment form to defaults"""
        self.assessment_form = {
            "patient_id": 0,
            "assessment_date": "",
            "chief_complaint": "",
            "history_present_illness": "",
            "primary_diagnosis": "",
            "secondary_diagnoses": "",
            "treatment_plan": ""
        }
        self.selected_disorder = ""
        self.disorder_criteria = None
        self.criterion_responses = {}
        self.dsm5_evaluation = None
        self.current_assessment = None
    
    def show_success(self, message: str):
        """Show success notification"""
        self.success_message = message
    
    def show_error(self, message: str):
        """Show error notification"""
        self.error_message = message
    
    def clear_messages(self):
        """Clear all notification messages"""
        self.error_message = ""
        self.success_message = ""
    
    @rx.var
    def criteria_met_percentage(self) -> float:
        """Calculate percentage of criteria met for progress display"""
        if not self.dsm5_evaluation:
            return 0.0
        
        total_criteria = len(self.criterion_responses)
        if total_criteria == 0:
            return 0.0
        
        met_count = self.dsm5_evaluation.get('met_criteria_count', 0)
        return (met_count / total_criteria) * 100
    
    @rx.var
    def diagnosis_confidence(self) -> str:
        """Get diagnosis confidence level as string"""
        if not self.dsm5_evaluation:
            return "Not evaluated"
        
        if self.dsm5_evaluation.get('criteria_met', False):
            confidence = self.dsm5_evaluation.get('confidence_level', 0)
            if confidence >= 0.8:
                return "High confidence"
            elif confidence >= 0.6:
                return "Moderate confidence"
            else:
                return "Low confidence"
        else:
            return "Criteria not met"
    
    @rx.var
    def required_criteria_status(self) -> str:
        """Get status of required criteria"""
        if not self.dsm5_evaluation:
            return ""
        
        met = self.dsm5_evaluation.get('met_criteria_count', 0)
        required = self.dsm5_evaluation.get('required_criteria', 0)
        
        return f"{met}/{required} criteria met"
```

## DSM-5-TR Configuration File

```yaml
# config/dsm5_criteria.yaml
version: '5-TR'
disorders:
  - disorder_code: '296.22'
    name: 'Major Depressive Disorder, Single Episode, Moderate'
    minimum_criteria: 5
    duration_required: true
    criteria:
      - id: 'mdd_1'
        description: 'Depressed mood most of the day, nearly every day'
        required: true
      - id: 'mdd_2' 
        description: 'Markedly diminished interest or pleasure in activities'
        required: true
      - id: 'mdd_3'
        description: 'Significant weight loss/gain or appetite changes'
        required: false
      - id: 'mdd_4'
        description: 'Insomnia or hypersomnia nearly every day'
        required: false
      - id: 'mdd_5'
        description: 'Psychomotor agitation or retardation'
        required: false
      - id: 'mdd_6'
        description: 'Fatigue or loss of energy nearly every day'
        required: false
      - id: 'mdd_7'
        description: 'Feelings of worthlessness or inappropriate guilt'
        required: false
      - id: 'mdd_8'
        description: 'Diminished ability to think or concentrate'
        required: false
      - id: 'mdd_9'
        description: 'Recurrent thoughts of death or suicidal ideation'
        required: false

  - disorder_code: '300.02'
    name: 'Generalized Anxiety Disorder'
    minimum_criteria: 3
    duration_required: true
    criteria:
      - id: 'gad_1'
        description: 'Excessive anxiety and worry, occurring more days than not for at least 6 months'
        required: true
      - id: 'gad_2'
        description: 'Difficulty controlling the worry'
        required: true
      - id: 'gad_3'
        description: 'Restlessness or feeling keyed up or on edge'
        required: false
      - id: 'gad_4'
        description: 'Being easily fatigued'
        required: false
      - id: 'gad_5'
        description: 'Difficulty concentrating or mind going blank'
        required: false
      - id: 'gad_6'
        description: 'Irritability'
        required: false
      - id: 'gad_7'
        description: 'Muscle tension'
        required: false
      - id: 'gad_8'
        description: 'Sleep disturbance'
        required: false

  - disorder_code: '309.81'
    name: 'Posttraumatic Stress Disorder'
    minimum_criteria: 1
    duration_required: true
    criteria:
      - id: 'ptsd_1'
        description: 'Exposure to actual or threatened death, serious injury, or sexual violence'
        required: true
      - id: 'ptsd_2'
        description: 'Intrusive memories, dreams, or flashbacks'
        required: false
      - id: 'ptsd_3'
        description: 'Avoidance of trauma-related stimuli'
        required: false
      - id: 'ptsd_4'
        description: 'Negative alterations in cognitions and mood'
        required: false
      - id: 'ptsd_5'
        description: 'Alterations in arousal and reactivity'
        required: false
```

## State Integration Notes

### Key Features

1. **Pydantic Integration**: All data validated with Pydantic models
2. **Real-time Evaluation**: DSM-5-TR criteria evaluated as user responds
3. **Progress Tracking**: Visual indicators for criteria completion
4. **Form Validation**: Comprehensive client-side validation
5. **Error Handling**: User-friendly error messages with logging
6. **State Persistence**: Maintains form state during navigation

### Usage in Reflex Components

```python
# Example component usage
@rx.page("/assessment/{patient_id}")
def assessment_page(patient_id: int):
    return rx.container(
        rx.cond(
            ClinicalState.is_loading,
            rx.spinner(),
            assessment_form_component()
        ),
        # Toast notifications
        rx.cond(
            ClinicalState.success_message,
            rx.toast(ClinicalState.success_message, status="success")
        ),
        rx.cond(
            ClinicalState.error_message,
            rx.toast(ClinicalState.error_message, status="error")
        )
    )

def assessment_form_component():
    return rx.form(
        # Disorder selection
        rx.select(
            ClinicalState.available_disorders,
            on_change=ClinicalState.select_disorder
        ),
        
        # DSM-5-TR criteria checkboxes
        rx.cond(
            ClinicalState.disorder_criteria,
            criteria_checklist_component()
        ),
        
        # Progress indicator
        rx.progress(
            value=ClinicalState.criteria_met_percentage,
            color_scheme="blue"
        ),
        
        # Diagnosis confidence
        rx.text(ClinicalState.diagnosis_confidence),
        
        on_submit=ClinicalState.create_assessment
    )
```