"""
Psychiatry EMR - Patient State Management
Enhanced state management with Pydantic data contracts and toast notifications.
"""

import reflex as rx
from typing import Optional, List, Dict, Any
import logging
from datetime import datetime

from models.patient import PatientData

logger = logging.getLogger(__name__)

class PatientState(rx.State):
    """Patient management state with Pydantic data contracts and toast notifications"""
    
    # Use Pydantic model instead of dict
    current_patient: Optional[PatientData] = None
    search_term: str = ""
    search_results: List[PatientData] = []
    potential_duplicates: List[dict] = []
    
    # Pagination
    current_page: int = 1
    page_size: int = 25
    total_results: int = 0
    total_pages: int = 0
    
    # UI state
    is_loading: bool = False
    error_message: str = ""
    success_message: str = ""  # For toast notifications
    
    @rx.var
    def current_user_id(self) -> int:
        """Get current user ID from JWT token"""
        if self.router.session.client_token:
            # Extract from JWT - implementation depends on Reflex auth structure
            return 1  # Placeholder
        return 0
    
    def show_success(self, message: str):
        """Show success toast notification"""
        self.success_message = message
        # Auto-clear after 3 seconds (would need timer in real implementation)
    
    def show_error(self, message: str):
        """Show error toast notification"""
        self.error_message = message
    
    def clear_messages(self):
        """Clear all notification messages"""
        self.error_message = ""
        self.success_message = ""
    
    def search_patients(self, term: str = None, page: int = None):
        """Search patients with pagination and proper error handling"""
        if term is not None:
            self.search_term = term
            self.current_page = 1  # Reset to first page on new search
        
        if page is not None:
            self.current_page = page
        
        if not self.search_term.strip():
            self.search_results = []
            self.total_results = 0
            self.total_pages = 0
            return
        
        self.is_loading = True
        self.clear_messages()
        
        try:
            # This would be implemented with proper dependency injection
            # For now, we'll create a placeholder implementation
            logger.info(f"Search completed: placeholder implementation")
            self.search_results = []
            self.total_results = 0
            self.total_pages = 0
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            self.show_error("Search failed. Please try again.")
            self.search_results = []
            self.total_results = 0
            self.total_pages = 0
        finally:
            self.is_loading = False
    
    def load_patient(self, patient_id: int):
        """Load patient with comprehensive error handling"""
        self.is_loading = True
        self.clear_messages()
        
        try:
            # This would be implemented with proper dependency injection
            # For now, we'll create a placeholder implementation
            logger.info(f"Patient load: placeholder implementation for ID {patient_id}")
            self.show_success(f"Patient loading functionality will be implemented in Phase 5")
                
        except Exception as e:
            logger.error(f"Failed to load patient {patient_id}: {e}")
            self.show_error("Failed to load patient data")
        finally:
            self.is_loading = False
    
    def create_patient(self, patient_data: dict):
        """Create new patient with validation"""
        self.is_loading = True
        self.clear_messages()
        
        try:
            # Validate with Pydantic
            validated_data = PatientData(**patient_data)
            
            # This would be implemented with proper dependency injection
            # For now, we'll create a placeholder implementation
            logger.info(f"Patient creation: placeholder implementation")
            self.show_success(f"Patient creation functionality will be implemented in Phase 5")
            
        except ValueError as e:
            logger.warning(f"Validation error: {e}")
            self.show_error(f"Invalid data: {e}")
        except Exception as e:
            logger.error(f"Failed to create patient: {e}")
            self.show_error("Failed to create patient")
        finally:
            self.is_loading = False
    
    def check_duplicates(self, patient_data: dict):
        """Check for potential duplicate patients"""
        try:
            validated_data = PatientData(**patient_data)
            
            # This would be implemented with proper dependency injection
            # For now, we'll create a placeholder implementation
            logger.info(f"Duplicate check: placeholder implementation")
            self.potential_duplicates = []
                
        except Exception as e:
            logger.error(f"Duplicate check failed: {e}")
            self.potential_duplicates = []
    
    def next_page(self):
        """Go to next page of search results"""
        if self.current_page < self.total_pages:
            self.search_patients(page=self.current_page + 1)
    
    def prev_page(self):
        """Go to previous page of search results"""
        if self.current_page > 1:
            self.search_patients(page=self.current_page - 1)
    
    def go_to_page(self, page: int):
        """Go to specific page"""
        if 1 <= page <= self.total_pages:
            self.search_patients(page=page)
