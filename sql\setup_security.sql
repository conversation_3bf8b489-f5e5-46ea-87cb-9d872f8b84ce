-- Psychiatry EMR - Database Security Setup
-- Enhanced security setup for PostgreSQL

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS pgcrypto;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pg_trgm;  -- For text search performance

-- Create audit trigger function
CREATE OR REPLACE FUNCTION create_audit_trail()
RETURNS TRIGGER AS $audit$
BEGIN
    -- Log all data changes
    IF TG_OP = 'DELETE' THEN
        INSERT INTO audit_log (
            user_id, table_name, record_id, action, 
            old_values, timestamp
        ) VALUES (
            COALESCE(current_setting('app.current_user', true)::int, 0),
            TG_TABLE_NAME,
            OLD.id,
            TG_OP,
            row_to_json(OLD),
            NOW()
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_log (
            user_id, table_name, record_id, action,
            old_values, new_values, timestamp
        ) VALUES (
            COALESCE(current_setting('app.current_user', true)::int, 0),
            TG_TABLE_NAME,
            NEW.id,
            TG_OP,
            row_to_json(OLD),
            row_to_json(NEW),
            NOW()
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO audit_log (
            user_id, table_name, record_id, action,
            new_values, timestamp
        ) VALUES (
            COALESCE(current_setting('app.current_user', true)::int, 0),
            TG_TABLE_NAME,
            NEW.id,
            TG_OP,
            row_to_json(NEW),
            NOW()
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$audit$ LANGUAGE plpgsql;

-- Apply audit triggers to sensitive tables (will be created when tables exist)
-- CREATE TRIGGER patient_audit_trigger
--     AFTER INSERT OR UPDATE OR DELETE ON patient
--     FOR EACH ROW EXECUTE FUNCTION create_audit_trail();

-- CREATE TRIGGER user_patient_access_audit_trigger
--     AFTER INSERT OR UPDATE OR DELETE ON user_patient_access
--     FOR EACH ROW EXECUTE FUNCTION create_audit_trail();

-- Row Level Security policies (will be applied when tables exist)
-- ALTER TABLE patient ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE user_patient_access ENABLE ROW LEVEL SECURITY;

-- Patients can only be accessed by authorized users
-- CREATE POLICY patient_access_policy ON patient
--     FOR ALL TO application_role
--     USING (
--         EXISTS (
--             SELECT 1 FROM user_patient_access upa 
--             WHERE upa.user_id = COALESCE(current_setting('app.current_user', true)::int, 0)
--             AND upa.patient_id = patient.id
--             AND upa.is_active = true
--         )
--     );

-- Users can only see their own access records
-- CREATE POLICY user_access_policy ON user_patient_access
--     FOR ALL TO application_role
--     USING (user_id = COALESCE(current_setting('app.current_user', true)::int, 0));

-- Create application role (commented out for now)
-- CREATE ROLE application_role;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO application_role;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO application_role;
