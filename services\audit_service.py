"""
Psychiatry EMR - Audit Service
Comprehensive audit logging service for security and compliance.
"""

from sqlmodel import Session
from datetime import datetime
from typing import Optional, Dict, Any
import logging
import json

from models.audit import AuditLog

logger = logging.getLogger(__name__)

class AuditService:
    """Service for comprehensive audit logging"""
    
    def __init__(self, db_session: Session, user_id: int):
        self.db = db_session
        self.user_id = user_id
    
    def log_action(
        self,
        action: str,
        table_name: str,
        record_id: Optional[int] = None,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ) -> None:
        """Log an action with comprehensive details"""
        try:
            audit_log = AuditLog(
                user_id=self.user_id,
                action=action,
                table_name=table_name,
                record_id=record_id,
                old_values=json.dumps(old_values) if old_values else None,
                new_values=json.dumps(new_values) if new_values else None,
                success=success,
                error_message=error_message
            )
            
            self.db.add(audit_log)
            self.db.commit()
            
            # Log to application logs as well
            log_message = f"AUDIT: {action} on {table_name}"
            if record_id:
                log_message += f" (ID: {record_id})"
            log_message += f" by user {self.user_id}"
            
            if success:
                logger.info(log_message)
            else:
                logger.warning(f"{log_message} - FAILED: {error_message}")
                
        except Exception as e:
            # Audit logging should never break the application
            logger.error(f"Failed to write audit log: {e}")
