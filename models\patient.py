"""
Psychiatry EMR - Patient Data Models
Enhanced data models with Pydantic integration for patient management.
"""

from __future__ import annotations
import reflex as rx
from sqlmodel import Field, Relationship, UniqueConstraint
from datetime import date, datetime
from typing import Optional, List
from pydantic import BaseModel, validator, EmailStr
import logging
import re

logger = logging.getLogger(__name__)

# Pydantic models for data contracts
class PatientData(BaseModel):
    """Pydantic model for patient data validation and serialization"""
    id: Optional[int] = None
    name: str
    dob: date
    address: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[EmailStr] = None  # Use EmailStr for validation
    education: Optional[str] = None
    occupation: Optional[str] = None
    living_situation: Optional[str] = None
    created_at: Optional[datetime] = None
    
    @validator('name')
    def name_must_not_be_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('Name cannot be empty')
        return v.strip()
    
    @validator('phone')
    def format_phone(cls, v):
        if not v:
            return None
        
        # Extract digits only
        digits = ''.join(filter(str.isdigit, v))
        
        # US phone number validation
        if len(digits) == 10:
            return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
        elif len(digits) == 11 and digits[0] == '1':
            return f"+1 ({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
        else:
            # Invalid phone number format
            raise ValueError('Phone number must be 10 digits (US format)')

class Patient(rx.Model, table=True):
    """Patient model with encrypted fields and unique constraints"""
    __table_args__ = (
        UniqueConstraint('name_encrypted', 'dob', name='unique_patient_name_dob'),
    )
    
    id: Optional[int] = Field(default=None, primary_key=True)
    name_encrypted: str  # Encrypted in service layer
    dob: date = Field(index=True)  # Add index for common queries
    address_encrypted: Optional[str] = Field(default=None)
    phone_encrypted: Optional[str] = Field(default=None)
    email_encrypted: Optional[str] = Field(default=None)
    education: Optional[str] = Field(default=None)
    occupation: Optional[str] = Field(default=None)
    living_situation: Optional[str] = Field(default=None)
    created_at: datetime = Field(default_factory=datetime.now, index=True)
    updated_by: Optional[int] = Field(default=None, foreign_key="user.id")
    is_active: bool = Field(default=True)  # For soft deletes
    merged_into: Optional[int] = Field(default=None, foreign_key="patient.id")  # Track merges
    
    # Relationships with explicit cascade control
    user_access: List["UserPatientAccess"] = Relationship(
        back_populates="patient",
        sa_relationship_kwargs={"cascade": "save-update, merge"}  # Safer cascade
    )
    clinical_assessments: List["PresentIllness"] = Relationship(
        back_populates="patient",
        sa_relationship_kwargs={"cascade": "save-update, merge, delete"}
    )

class UserPatientAccess(rx.Model, table=True):
    """User access control with audit trail"""
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id")
    patient_id: int = Field(foreign_key="patient.id")
    granted_at: datetime = Field(default_factory=datetime.now)
    granted_by: int = Field(foreign_key="user.id")
    is_active: bool = Field(default=True)
    
    # Relationships
    patient: Optional[Patient] = Relationship(back_populates="user_access")
