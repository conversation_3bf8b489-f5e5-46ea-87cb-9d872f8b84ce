"""
Psychiatry EMR - DSM-5-TR Rule Engine
Enhanced DSM-5-TR rule engine with validation and comprehensive disorder support.
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pydantic import BaseModel, validator
import yaml
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class CriterionRule(BaseModel):
    """Individual DSM-5-TR criterion with Pydantic validation"""
    id: str
    description: str
    required: bool = True
    # Remove unused weight field - DSM-5-TR uses count-based criteria
    
    @validator('id')
    def id_must_be_valid(cls, v):
        if not v or not v.strip():
            raise ValueError('Criterion ID cannot be empty')
        return v.strip()

class DisorderCriteria(BaseModel):
    """Complete disorder criteria set with validation"""
    disorder_code: str
    name: str
    criteria: List[CriterionRule]
    minimum_criteria: int
    duration_required: bool = True
    exclusion_rules: List[str] = []
    
    @validator('minimum_criteria')
    def minimum_must_be_reasonable(cls, v, values):
        if 'criteria' in values and v > len(values['criteria']):
            raise ValueError('Minimum criteria cannot exceed total criteria count')
        if v < 1:
            raise ValueError('Minimum criteria must be at least 1')
        return v

class DSM5Configuration(BaseModel):
    """Complete DSM-5-TR configuration with validation"""
    version: str
    disorders: List[DisorderCriteria]

class DSM5RuleEngine:
    """Rule engine for DSM-5-TR diagnostic criteria with robust validation"""
    
    def __init__(self, criteria_file: str = "config/dsm5_criteria.yaml"):
        self.criteria_file = Path(criteria_file)
        self.disorders: Dict[str, DisorderCriteria] = {}
        self._load_and_validate_criteria()
    
    def _load_and_validate_criteria(self) -> None:
        """Load and validate DSM-5-TR criteria from YAML file"""
        try:
            if not self.criteria_file.exists():
                logger.info("DSM-5-TR criteria file not found, creating default")
                self._create_default_criteria_file()
            
            with open(self.criteria_file, 'r') as f:
                raw_data = yaml.safe_load(f)
            
            # Validate with Pydantic
            config = DSM5Configuration(**raw_data)
            
            # Build disorders dictionary
            for disorder in config.disorders:
                self.disorders[disorder.disorder_code] = disorder
            
            logger.info(f"Loaded {len(self.disorders)} DSM-5-TR disorders")
            
        except yaml.YAMLError as e:
            logger.error(f"YAML parsing error in DSM-5-TR criteria: {e}")
            raise ValueError(f"Invalid YAML in DSM-5-TR criteria file: {e}")
        except Exception as e:
            logger.error(f"Failed to load DSM-5-TR criteria: {e}")
            raise
    
    def evaluate_criteria(self, disorder_code: str, responses: Dict[str, bool]) -> Dict[str, Any]:
        """Evaluate diagnostic criteria and return results (count-based, not weight-based)"""
        if disorder_code not in self.disorders:
            raise ValueError(f"Unknown disorder code: {disorder_code}")
        
        disorder = self.disorders[disorder_code]
        met_criteria = []
        
        for criterion in disorder.criteria:
            if responses.get(criterion.id, False):
                met_criteria.append(criterion.id)
        
        criteria_met = len(met_criteria) >= disorder.minimum_criteria
        
        return {
            'disorder_code': disorder_code,
            'disorder_name': disorder.name,
            'criteria_met': criteria_met,
            'met_criteria_count': len(met_criteria),
            'required_criteria': disorder.minimum_criteria,
            'met_criteria_ids': met_criteria,
            'duration_required': disorder.duration_required,
            'confidence_level': len(met_criteria) / len(disorder.criteria) if disorder.criteria else 0
        }
    
    def get_disorder_criteria(self, disorder_code: str) -> Optional[DisorderCriteria]:
        """Get criteria for a specific disorder"""
        return self.disorders.get(disorder_code)
    
    def list_available_disorders(self) -> List[Dict[str, str]]:
        """List all available disorders"""
        return [
            {'code': code, 'name': disorder.name}
            for code, disorder in self.disorders.items()
        ]

    def _create_default_criteria_file(self):
        """Create default DSM-5-TR criteria file with comprehensive validation"""
        default_criteria = {
            'version': '5-TR',
            'disorders': [
                {
                    'disorder_code': '296.22',
                    'name': 'Major Depressive Disorder, Single Episode, Moderate',
                    'minimum_criteria': 5,
                    'duration_required': True,
                    'criteria': [
                        {'id': 'mdd_1', 'description': 'Depressed mood most of the day, nearly every day', 'required': True},
                        {'id': 'mdd_2', 'description': 'Markedly diminished interest or pleasure in activities', 'required': True},
                        {'id': 'mdd_3', 'description': 'Significant weight loss/gain or appetite changes'},
                        {'id': 'mdd_4', 'description': 'Insomnia or hypersomnia nearly every day'},
                        {'id': 'mdd_5', 'description': 'Psychomotor agitation or retardation'},
                        {'id': 'mdd_6', 'description': 'Fatigue or loss of energy nearly every day'},
                        {'id': 'mdd_7', 'description': 'Feelings of worthlessness or inappropriate guilt'},
                        {'id': 'mdd_8', 'description': 'Diminished ability to think or concentrate'},
                        {'id': 'mdd_9', 'description': 'Recurrent thoughts of death or suicidal ideation'}
                    ]
                },
                {
                    'disorder_code': '300.02',
                    'name': 'Generalized Anxiety Disorder',
                    'minimum_criteria': 3,
                    'duration_required': True,
                    'criteria': [
                        {'id': 'gad_1', 'description': 'Excessive anxiety and worry, occurring more days than not for at least 6 months', 'required': True},
                        {'id': 'gad_2', 'description': 'Difficulty controlling the worry', 'required': True},
                        {'id': 'gad_3', 'description': 'Restlessness or feeling keyed up or on edge'},
                        {'id': 'gad_4', 'description': 'Being easily fatigued'},
                        {'id': 'gad_5', 'description': 'Difficulty concentrating or mind going blank'},
                        {'id': 'gad_6', 'description': 'Irritability'},
                        {'id': 'gad_7', 'description': 'Muscle tension'},
                        {'id': 'gad_8', 'description': 'Sleep disturbance'}
                    ]
                }
            ]
        }

        # Ensure config directory exists
        self.criteria_file.parent.mkdir(parents=True, exist_ok=True)

        with open(self.criteria_file, 'w') as f:
            yaml.dump(default_criteria, f, default_flow_style=False, indent=2)
