"""
Psychiatry EMR - Database Connection Management
Database connection with connection pooling and performance optimization.
"""

from sqlmodel import Session, create_engine
from contextlib import contextmanager
from config.settings import get_settings
import logging

logger = logging.getLogger(__name__)

# Global engine instance - will be initialized when needed
_engine = None

def get_engine():
    """Get database engine with lazy initialization"""
    global _engine
    if _engine is None:
        settings = get_settings()
        
        # Database connection with connection pooling
        _engine = create_engine(
            settings.database_url,
            echo=settings.debug_mode,
            pool_size=settings.db_pool_size,
            max_overflow=settings.db_max_overflow,
            pool_pre_ping=True,  # Verify connections before use
            pool_recycle=3600,   # Recycle connections every hour
        )
        logger.info("Database engine initialized")
    
    return _engine

@contextmanager
def get_db_session():
    """Context manager for database sessions with proper cleanup"""
    engine = get_engine()
    session = Session(engine)
    try:
        yield session
    except Exception as e:
        session.rollback()
        logger.error(f"Database session error: {e}")
        raise
    finally:
        session.close()

# Additional database indexes for performance
DB_INDEXES = """
-- Performance indexes for common queries
CREATE INDEX IF NOT EXISTS idx_patient_dob ON patient(dob);
CREATE INDEX IF NOT EXISTS idx_patient_created_at ON patient(created_at);
CREATE INDEX IF NOT EXISTS idx_patient_active ON patient(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_audit_user_ts ON audit_log(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_table_record ON audit_log(table_name, record_id);

CREATE INDEX IF NOT EXISTS idx_user_access_patient ON user_patient_access(patient_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_access_user ON user_patient_access(user_id) WHERE is_active = true;

-- Partial index for active patients only (more efficient)
CREATE INDEX IF NOT EXISTS idx_patient_active_only ON patient(id) WHERE is_active = true;
"""
